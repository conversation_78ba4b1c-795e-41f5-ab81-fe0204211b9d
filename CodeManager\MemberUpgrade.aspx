<%@ Page Title="" Language="C#" AutoEventWireup="true" CodeBehind="MemberUpgrade.aspx.cs" Inherits="Account.Web.MemberUpgrade" %>

<%@ Import Namespace="CommonLib" %>

<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="icon" href="/favicon.ico">
    <title>开通VIP会员<%=PageTitleConst.Default_Ext_Short %></title>
    <link href="/static/css/upgrade.css" rel="stylesheet">
    <style type="text/css">
        .icon-v-1 {
            background: url(/static/image/vip_1.png) 0 center no-repeat;
            width: 45px;
            height: 32px;
            background-size: 100% auto;
            margin-right: 5px;
        }

        .icon-v1 {
            background: url(/static/image/vip_2.png) 0 center no-repeat;
            width: 45px;
            height: 32px;
            background-size: 100% auto;
            margin-right: 5px;
        }

        .icon-v3 {
            background: url(/static/image/vip_3.png) 0 center no-repeat;
            width: 50px;
            height: 32px;
            background-size: 100% auto;
            margin-right: 5px;
        }
    </style>
</head>
<body class="">
    <div>
        <section class="el-container main-container is-vertical openSidebar withoutAnimation">
            <section class="el-container" style="overflow: hidden;">
                <section class="el-container is-vertical" id="divGoToPay">
                    <main class="el-main main">
                        <div class="main-container">
                            <div data-v-140a455d="" class="main-container main-container1">

                                <div data-v-140a455d="" class="main-contain">
                                    <div data-v-140a455d="" class="flex">
                                        <div data-v-140a455d="" class="left-body" style="">
                                            <div data-v-140a455d="" class="left-content">
                                                <div data-v-0fd0c460="" data-v-140a455d="" class="order-item" id="divUserType">
                                                    <div data-v-0fd0c460="" class="title-box flex flex-center flex-between">
                                                        <div data-v-0fd0c460="" class="left">
                                                            <noscript>
                                                                <span data-v-0fd0c460="" class="large fr-fix-b4af68d8">您的浏览器不支持 JavaScript，请启用 JavaScript 后重试！</span>
                                                            </noscript>
                                                            <span data-v-0fd0c460="" class="large fr-fix-b4af68d8">选择升级类型</span>
                                                        </div>
                                                        <div data-v-0fd0c460="" class="right flex flex-center text-blue blue-hover pointer" onclick="openDialogByIframe('详细功能介绍','Version.aspx');">
                                                            <img data-v-0fd0c460="" src="/static/image/icon_hint_blue.svg" class="svg-icon hint">功能比较
                                                        </div>
                                                    </div>
                                                    <div data-v-0fd0c460="" class="item-wrap">
                                                        <%
                                                            var type = Request.GetValue("optype");
                                                            var lstUserType = Equals(type, "api") ? UserTypeHelper.GetCanRegApiTypes() : UserTypeHelper.GetCanRegUserTypes();
                                                            lstUserType.ForEach(p =>
                                                            {
                                                                var lstChargeType = new List<ChargeViewToUser>();
                                                                p.ChargeTypes.ForEach(q =>
                                                                {
                                                                    string strDesc = "";
                                                                    var price = q.GetPrice(p.PerPrice, ref strDesc);
                                                                    var charge = new ChargeViewToUser()
                                                                    {
                                                                        Name = q.Name,
                                                                        Desc = strDesc,
                                                                        Price = (double)price,
                                                                        OriPrice = (double)q.OriPrice,
                                                                        IsDefault = q.IsDefault,
                                                                        Tag = q.Tag,
                                                                    };
                                                                    lstChargeType.Add(charge);
                                                                });
                                                                p.UserChargeType = lstChargeType;
                                                            });

                                                            int colorIndex = -1;
                                                            foreach (var userType in lstUserType)
                                                            {
                                                                colorIndex++;
                                                        %>
                                                        <div data-v-0fd0c460="" id="change_type_<%=userType.Type.GetHashCode() %>" onclick="javascript:changeUserType('<%=userType.Type.GetHashCode() %>');" class="item-box flex flex-center" style="cursor: pointer;">
                                                            <img data-v-0fd0c460="" class="svg-icon iconCheck" src="<%=colorIndex==0?"/static/image/radio-checked1.svg":"/static/image/radio-unchecked.svg" %>">
                                                            <div data-v-0fd0c460="" class="right flex1">
                                                                <div data-v-0fd0c460="" class="flex flex-center">
                                                                    <i class="icon-v<%=userType.Type.GetHashCode() %>"></i>
                                                                    <span data-v-0fd0c460="" class="name" value="<%=userType.Name %>"><%=userType.Name %></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <%
                                                            }
                                                        %>
                                                    </div>
                                                    <div data-v-0fd0c460="" class="step">1</div>
                                                </div>
                                                <div data-v-4aa4ac8f="" data-v-140a455d="" class="order-item" id="divPayType">
                                                    <div data-v-4aa4ac8f="" class="title-box flex flex-center flex-between">
                                                        <div data-v-4aa4ac8f="" class="left"><span data-v-4aa4ac8f="" class="large fr-fix-b4af68d8">选择订阅方式</span></div>
                                                    </div>
                                                    <%
                                                        colorIndex = -1;
                                                        foreach (var userType in lstUserType)
                                                        {
                                                            colorIndex++;
                                                    %>
                                                    <div data-v-4aa4ac8f="" class="item-wrap" style="display: <%=colorIndex==0?"block":"none"%>" id="pay_type_<%=userType.Type.GetHashCode() %>">
                                                        <%foreach (var payType in userType.UserChargeType)
                                                            {
                                                        %>
                                                        <div data-v-4aa4ac8f="" class="item-box flex hoverItem flex-center" onclick="changePayType('<%=payType.Name %>');">
                                                            <div data-v-4aa4ac8f="" class="tipDiv">
                                                                <img data-v-4aa4ac8f="" id="pay_item_<%=payType.Name %>" default="<%=payType.IsDefault?1:0 %>" class="svg-icon iconCheck" src="<%=colorIndex==0?"/static/image/radio-checked1.svg":"/static/image/radio-unchecked.svg" %>">
                                                            </div>
                                                            <div data-v-4aa4ac8f="" class="right-box flex1">
                                                                <div data-v-4aa4ac8f="" class="top flex flex-center flex-between">
                                                                    <div data-v-4aa4ac8f="" class="left flex flex-center">
                                                                        <span data-v-4aa4ac8f="" class="name" value="<%=payType.Name %>"><%=payType.Name %></span>
                                                                        <span data-v-4aa4ac8f="" class="mark"><%=Equals(payType.Tag,"recommond")?"推荐":Equals(payType.Tag,"hot")?"热门":Equals(payType.Tag,"_new")?"新":"" %></span>
                                                                    </div>
                                                                    <div data-v-4aa4ac8f="" class="right flex flex-center">
                                                                        <div data-v-4aa4ac8f="" class="oldPrice throu" value="<%=payType.OriPrice.ToString("F0") %>元"><%=payType.OriPrice.ToString("F0") %>元</div>
                                                                        <div data-v-4aa4ac8f="" class="money fr-fix-b4af68d8">
                                                                            ¥
                                                                            <span data-v-4aa4ac8f="" class="fr-fix-b4af68d8" value="<%=payType.Price %>元"><%=payType.Price %>元</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div data-v-4aa4ac8f="" class="tip flex flex-center flex-between">
                                                                    <%
                                                                        var discountOriPrice = Math.Min(payType.Price / payType.OriPrice, userType.YearDiscount); %>
                                                                    <div data-v-4aa4ac8f="" class="descInfo"><%=payType.Desc %></div>
                                                                    <div data-v-4aa4ac8f="" class="discountInfo">限时<%=(discountOriPrice * 10).ToString("F0").TrimEnd('0').TrimEnd('.') %>折优惠</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <%
                                                            } %>
                                                    </div>
                                                    <%
                                                        }
                                                    %>
                                                    <div data-v-4aa4ac8f="" class="step">2</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div data-v-140a455d="" id="right" class="right-body" style="">
                                            <div data-v-1e6a1650="" data-v-140a455d="" class="wrap border-b scrollDetail" id="detail">
                                                <div data-v-1e6a1650="" class="title fr-fix-b4af68d8">您的升级方案：</div>
                                                <%var defaultUserType = lstUserType[0];  %>
                                                <dl data-v-1e6a1650="" class="box1 border-b bbox">
                                                    <dt data-v-1e6a1650="" class="fr-fix-b4af68d8"><span data-v-1e6a1650="" class="fr-fix-b4af68d8" id="lblUserType">
                                                        <%=defaultUserType.UserChargeType.FirstOrDefault().Name+defaultUserType.Name %>
                                                    </span>
                                                    </dt>

                                                    <dd data-v-1e6a1650="" class="time flex flex-center">
                                                        <p data-v-1e6a1650="" class="p3"><%=defaultUserType.UserChargeType.FirstOrDefault().Desc %></p>
                                                    </dd>
                                                    <dd data-v-1e6a1650="" class="time flex flex-center">
                                                        <p data-v-1e6a1650="" class="p1">限时优惠</p>
                                                    </dd>
                                                </dl>

                                            </div>
                                            <div data-v-66329e6c="" data-v-140a455d="" class="footer scrollFooter" id="footer" actual-price="19">
                                                <div data-v-66329e6c="" class="total">
                                                    <div data-v-66329e6c="" class="footer-right-order">
                                                        <div data-v-66329e6c="" class="flex flex-between top">
                                                            <div data-v-66329e6c="" class="flex"><span data-v-66329e6c="" class="sp1">金额</span></div>
                                                            <div data-v-66329e6c="">
                                                                <p data-v-66329e6c="" class="p1 fr-fix-b4af68d8">
                                                                    <span data-v-66329e6c="" class="totalPrice">
                                                                        <i data-v-66329e6c="">¥</i>
                                                                        <%=defaultUserType.UserChargeType.FirstOrDefault().OriPrice.ToString("F0") %></span> ¥
                                                                    <span data-v-66329e6c="" class="price fr-fix-b4af68d8"><%=defaultUserType.UserChargeType.FirstOrDefault().Price.ToString("F0") %></span>
                                                                </p>
                                                                <div data-v-66329e6c="" class="couponPrice">优惠：<span data-v-66329e6c="">¥<%=(defaultUserType.UserChargeType.FirstOrDefault().OriPrice-defaultUserType.UserChargeType.FirstOrDefault().Price).ToString("F0") %>.00</span></div>
                                                            </div>
                                                        </div>
                                                        <div data-v-66329e6c="" class="coup-box" style="display: none;">
                                                            <div data-v-66329e6c="" class="coupon">
                                                                <div data-v-66329e6c="" class="input el-input">
                                                                    <input type="text" autocomplete="off" id="txtAccount" maxlength="50" placeholder="请输入您的账号（手机号/邮箱）" class="el-input__inner">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <button data-v-66329e6c="" type="button" class="el-button btn el-button--primary fr-fix-b4af68d8" onclick="submitToPay()">
                                                            <span class="fr-fix-b4af68d8">确定付款</span></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </section>
            </section>
        </section>
    </div>


    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//cdn.bootcdn.net/ajax/libs/jquery/1.4.2/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script type="text/javascript">
