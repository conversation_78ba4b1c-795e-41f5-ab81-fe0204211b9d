﻿<%@ Page Title="会员升级 - OCR文字识别助手" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手会员升级页面，选择适合您的VIP等级，解锁更多高级功能和专属服务。" />
    <meta name="keywords" content="OCR会员升级,VIP会员,OCR付费版本,文字识别会员,OCR专业版" />
    <style>
        .upgrade-page-container{max-width:1200px;margin:0 auto;padding:80px 20px 20px 20px;min-height:600px}
        .upgrade-hero{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-radius:15px;padding:50px 40px;text-align:center;color:white;margin-bottom:50px;position:relative;overflow:hidden}
        .upgrade-hero::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');opacity:0.3}
        .upgrade-hero h1{font-size:2.8rem;font-weight:bold;margin-bottom:15px;position:relative;z-index:1}
        .upgrade-hero p{font-size:1.1rem;margin-bottom:0;opacity:0.9;position:relative;z-index:1}
        .pricing-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(320px,1fr));gap:30px;margin-bottom:50px}
        .pricing-card{background:white;border-radius:15px;padding:0;box-shadow:0 8px 30px rgba(0,0,0,0.1);border:2px solid transparent;transition:all 0.3s;position:relative;overflow:hidden}
        .pricing-card:hover{transform:translateY(-5px);box-shadow:0 15px 40px rgba(0,0,0,0.15)}
        .pricing-card.recommended{border-color:#ff6b35;transform:scale(1.05)}
        .pricing-card.recommended::before{content:'推荐';position:absolute;top:20px;right:-30px;background:#ff6b35;color:white;padding:5px 40px;font-size:0.9rem;font-weight:bold;transform:rotate(45deg);z-index:2}
        .pricing-header{padding:30px 30px 20px;text-align:center;position:relative}
        .pricing-card.free .pricing-header{background:linear-gradient(135deg,#28a745,#20c997)}
        .pricing-card.personal .pricing-header{background:linear-gradient(135deg,#007cfa,#0056b3)}
        .pricing-card.professional .pricing-header{background:linear-gradient(135deg,#6f42c1,#5a2d91)}
        .pricing-card.enterprise .pricing-header{background:linear-gradient(135deg,#fd7e14,#e55a2b)}
        .pricing-title{color:white;font-size:1.5rem;font-weight:bold;margin-bottom:10px}
        .pricing-price{color:white;font-size:2.5rem;font-weight:bold;margin-bottom:5px}
        .pricing-price .currency{font-size:1.2rem}
        .pricing-price .period{font-size:1rem;font-weight:normal;opacity:0.8}
        .pricing-subtitle{color:white;font-size:0.9rem;opacity:0.8}
        .pricing-body{padding:30px}
        .pricing-features{list-style:none;padding:0;margin:0 0 30px 0}
        .pricing-features li{padding:10px 0;color:#666;border-bottom:1px solid #f0f0f0;display:flex;align-items:center;font-size:0.95rem}
        .pricing-features li:last-child{border-bottom:none}
        .pricing-features li::before{content:'✓';color:#28a745;font-weight:bold;margin-right:12px;font-size:1.1rem}
        .pricing-features li.unavailable{opacity:0.5}
        .pricing-features li.unavailable::before{content:'✗';color:#dc3545}
        .pricing-btn{display:block;width:100%;padding:15px;border:none;border-radius:8px;font-size:1.1rem;font-weight:600;text-decoration:none;text-align:center;transition:all 0.3s;cursor:pointer}
        .pricing-btn.btn-free{background:#28a745;color:white}
        .pricing-btn.btn-free:hover{background:#218838;color:white;text-decoration:none}
        .pricing-btn.btn-personal{background:#007cfa;color:white}
        .pricing-btn.btn-personal:hover{background:#0056b3;color:white;text-decoration:none}
        .pricing-btn.btn-professional{background:#6f42c1;color:white}
        .pricing-btn.btn-professional:hover{background:#5a2d91;color:white;text-decoration:none}
        .pricing-btn.btn-enterprise{background:#fd7e14;color:white}
        .pricing-btn.btn-enterprise:hover{background:#e55a2b;color:white;text-decoration:none}
        .features-comparison{background:#f8f9fa;border-radius:15px;padding:40px;margin-bottom:40px}
        .features-comparison h3{text-align:center;color:#333;font-size:2rem;margin-bottom:30px}
        .comparison-table{width:100%;border-collapse:collapse;background:white;border-radius:10px;overflow:hidden;box-shadow:0 4px 20px rgba(0,0,0,0.1)}
        .comparison-table th,.comparison-table td{padding:15px;text-align:center;border-bottom:1px solid #e9ecef}
        .comparison-table th{background:#007cfa;color:white;font-weight:600}
        .comparison-table tr:hover{background:#f8f9fa}
        .comparison-table .feature-name{text-align:left;font-weight:500;color:#333}
        .comparison-table .check{color:#28a745;font-size:1.2rem;font-weight:bold}
        .comparison-table .cross{color:#dc3545;font-size:1.2rem;font-weight:bold}
        .faq-section{background:white;border-radius:15px;padding:40px;box-shadow:0 4px 20px rgba(0,0,0,0.1)}
        .faq-section h3{text-align:center;color:#333;font-size:2rem;margin-bottom:30px}
        .faq-item{margin-bottom:20px;border:1px solid #e9ecef;border-radius:8px;overflow:hidden}
        .faq-question{background:#f8f9fa;padding:20px;cursor:pointer;font-weight:600;color:#333;display:flex;justify-content:space-between;align-items:center;margin:0}
        .faq-question:hover{background:#e9ecef}
        .faq-answer{padding:20px;background:white;display:none}
        .faq-answer.show{display:block}
        .faq-toggle{font-size:1.2rem;transition:transform 0.3s}
        .faq-question.active .faq-toggle{transform:rotate(180deg)}
        @media (max-width:768px){
            .upgrade-page-container{padding:15px}
            .upgrade-hero{padding:30px 20px}
            .upgrade-hero h1{font-size:2rem}
            .pricing-grid{grid-template-columns:1fr;gap:20px}
            .pricing-card.recommended{transform:none}
            .features-comparison,.faq-section{padding:20px}
            .comparison-table{font-size:0.9rem}
            .comparison-table th,.comparison-table td{padding:10px 8px}
        }
    </style>
    <script>
        function toggleFAQ(element) {
            const answer = element.nextElementSibling;
            const toggle = element.querySelector('.faq-toggle');
            
            // 关闭其他FAQ
            document.querySelectorAll('.faq-question.active').forEach(q => {
                if (q !== element) {
                    q.classList.remove('active');
                    q.nextElementSibling.classList.remove('show');
                }
            });
            
            // 切换当前FAQ
            element.classList.toggle('active');
            answer.classList.toggle('show');
        }
    </script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="upgrade-page-container">
        <!-- 升级主区域 -->
        <div class="upgrade-hero">
            <h1>选择适合您的VIP等级</h1>
            <p>解锁更多高级功能，享受专属服务，让工作效率翻倍提升</p>
        </div>

        <!-- 价格卡片 -->
        <div class="pricing-grid">
            <!-- 免费版 -->
            <div class="pricing-card free">
                <div class="pricing-header">
                    <div class="pricing-title">免费版</div>
                    <div class="pricing-price">
                        <span class="currency">¥</span>0
                        <span class="period">/永久</span>
                    </div>
                    <div class="pricing-subtitle">体验基础功能</div>
                </div>
                <div class="pricing-body">
                    <ul class="pricing-features">
                        <li>每天20次截图识别</li>
                        <li>基础文字识别</li>
                        <li>在线识别功能</li>
                        <li class="unavailable">离线识别</li>
                        <li class="unavailable">表格识别</li>
                        <li class="unavailable">公式识别</li>
                        <li class="unavailable">批量处理</li>
                    </ul>
                    <a href="<%=Account.Web.CommonRequest.GetDownLoadUrl(Request) %>" class="pricing-btn btn-free">
                        免费下载
                    </a>
                </div>
            </div>

            <!-- 个人版 -->
            <div class="pricing-card personal">
                <div class="pricing-header">
                    <div class="pricing-title">个人版</div>
                    <div class="pricing-price">
                        <span class="currency">¥</span>29
                        <span class="period">/月</span>
                    </div>
                    <div class="pricing-subtitle">适合个人用户</div>
                </div>
                <div class="pricing-body">
                    <ul class="pricing-features">
                        <li>每天200次识别额度</li>
                        <li>离线识别功能</li>
                        <li>多语言翻译</li>
                        <li>2台设备授权</li>
                        <li class="unavailable">表格识别</li>
                        <li class="unavailable">公式识别</li>
                        <li class="unavailable">批量处理</li>
                    </ul>
                    <a href="UserUpgrade.aspx" class="pricing-btn btn-personal">
                        立即升级
                    </a>
                </div>
            </div>

            <!-- 专业版 -->
            <div class="pricing-card professional recommended">
                <div class="pricing-header">
                    <div class="pricing-title">专业版</div>
                    <div class="pricing-price">
                        <span class="currency">¥</span>59
                        <span class="period">/月</span>
                    </div>
                    <div class="pricing-subtitle">最受欢迎的选择</div>
                </div>
                <div class="pricing-body">
                    <ul class="pricing-features">
                        <li>每天500次识别额度</li>
                        <li>表格识别转Excel</li>
                        <li>公式识别功能</li>
                        <li>批量处理</li>
                        <li>3台设备授权</li>
                        <li>优先客服支持</li>
                        <li class="unavailable">API接口</li>
                    </ul>
                    <a href="UserUpgrade.aspx" class="pricing-btn btn-professional">
                        立即升级
                    </a>
                </div>
            </div>

            <!-- 旗舰版 -->
            <div class="pricing-card enterprise">
                <div class="pricing-header">
                    <div class="pricing-title">旗舰版</div>
                    <div class="pricing-price">
                        <span class="currency">¥</span>99
                        <span class="period">/月</span>
                    </div>
                    <div class="pricing-subtitle">企业级体验</div>
                </div>
                <div class="pricing-body">
                    <ul class="pricing-features">
                        <li>每天2000次识别额度</li>
                        <li>文档识别处理</li>
                        <li>API接口调用</li>
                        <li>5台设备授权</li>
                        <li>专属客服支持</li>
                        <li>定制化服务</li>
                        <li>数据导出功能</li>
                    </ul>
                    <a href="UserUpgrade.aspx" class="pricing-btn btn-enterprise">
                        立即升级
                    </a>
                </div>
            </div>
        </div>

        <!-- 功能对比表 -->
        <div class="features-comparison">
            <h3>功能详细对比</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th class="feature-name">功能特性</th>
                        <th>免费版</th>
                        <th>个人版</th>
                        <th>专业版</th>
                        <th>旗舰版</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="feature-name">每日识别次数</td>
                        <td>20次</td>
                        <td>200次</td>
                        <td>500次</td>
                        <td>2000次</td>
                    </tr>
                    <tr>
                        <td class="feature-name">离线识别</td>
                        <td class="cross">✗</td>
                        <td class="check">✓</td>
                        <td class="check">✓</td>
                        <td class="check">✓</td>
                    </tr>
                    <tr>
                        <td class="feature-name">表格识别</td>
                        <td class="cross">✗</td>
                        <td class="cross">✗</td>
                        <td class="check">✓</td>
                        <td class="check">✓</td>
                    </tr>
                    <tr>
                        <td class="feature-name">公式识别</td>
                        <td class="cross">✗</td>
                        <td class="cross">✗</td>
                        <td class="check">✓</td>
                        <td class="check">✓</td>
                    </tr>
                    <tr>
                        <td class="feature-name">批量处理</td>
                        <td class="cross">✗</td>
                        <td class="cross">✗</td>
                        <td class="check">✓</td>
                        <td class="check">✓</td>
                    </tr>
                    <tr>
                        <td class="feature-name">设备授权数</td>
                        <td>1台</td>
                        <td>2台</td>
                        <td>3台</td>
                        <td>5台</td>
                    </tr>
                    <tr>
                        <td class="feature-name">客服支持</td>
                        <td>社区支持</td>
                        <td>邮件支持</td>
                        <td>优先支持</td>
                        <td>专属客服</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 常见问题 -->
        <div class="faq-section">
            <h3>常见问题</h3>
            
            <div class="faq-item">
                <div class="faq-question" onclick="toggleFAQ(this)">
                    如何选择适合的版本？
                    <span class="faq-toggle">▼</span>
                </div>
                <div class="faq-answer">
                    <p>根据您的使用频率和功能需求选择：</p>
                    <ul>
                        <li><strong>免费版</strong>：偶尔使用，每天识别量少于20次</li>
                        <li><strong>个人版</strong>：日常办公，需要离线识别和翻译功能</li>
                        <li><strong>专业版</strong>：频繁使用，需要表格和公式识别</li>
                        <li><strong>旗舰版</strong>：企业用户，需要大量处理和API接口</li>
                    </ul>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFAQ(this)">
                    支付后多久生效？
                    <span class="faq-toggle">▼</span>
                </div>
                <div class="faq-answer">
                    <p>支付成功后立即生效，所有设备自动同步会员状态。如遇问题请联系客服。</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFAQ(this)">
                    可以随时取消订阅吗？
                    <span class="faq-toggle">▼</span>
                </div>
                <div class="faq-answer">
                    <p>可以随时取消订阅，取消后在当前计费周期结束前仍可正常使用VIP功能。</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFAQ(this)">
                    支持哪些支付方式？
                    <span class="faq-toggle">▼</span>
                </div>
                <div class="faq-answer">
                    <p>支持微信支付、支付宝、银行卡等多种支付方式，安全便捷。</p>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
