﻿2025-08-02 00:00:13,206 [53] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1440): error CS1061: “CommonLib.UserLoginInfo”不包含“DtLastLogin”的定义，并且找不到可接受类型为“CommonLib.UserLoginInfo”的第一个参数的扩展方法“DtLastLogin”(是否缺少 using 指令或程序集引用?)
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 00:33:10,324 [56] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 00:33:10
2025-08-02 00:37:10,454 [104] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1399): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 00:37:22,298 [71] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1399): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 00:37:23,153 [108] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1399): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 00:37:56,982 [31] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1486): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 00:38:35,251 [106] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1567): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 00:40:56,830 [81] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 00:40:56
2025-08-02 00:41:04,424 [101] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1567): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 00:48:27,340 [107] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1643): error CS1525: 无效的表达式项“.”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:00:10,256 [110] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(859): error CS1525: 无效的表达式项“.”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:12:13,817 [34] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpParseException (0x80004005): ID“Content2”已被其他控件使用。 ---> System.Web.HttpParseException (0x80004005): ID“Content2”已被其他控件使用。 ---> System.Web.HttpException (0x80004005): ID“Content2”已被其他控件使用。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:12:41,609 [107] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(325): error CS1525: 无效的表达式项“.”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:13:10,977 [89] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(325): error CS1525: 无效的表达式项“.”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:14:10,956 [86] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(308): error CS1525: 无效的表达式项“.”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:14:41,303 [107] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(308): error CS1525: 无效的表达式项“.”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:15:09,971 [104] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(308): error CS1525: 无效的表达式项“.”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:15:10,824 [110] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(308): error CS1525: 无效的表达式项“.”
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 01:30:12,304 [107] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(285): error CS1061: “CommonLib.UserType”不包含“TypeName”的定义，并且找不到可接受类型为“CommonLib.UserType”的第一个参数的扩展方法“TypeName”(是否缺少 using 指令或程序集引用?)
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-02 12:38:07,908 [94] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 12:38:07
2025-08-02 13:18:50,390 [103] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 13:18:50
2025-08-02 13:40:40,082 [90] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 13:40:40
2025-08-02 14:48:37,264 [63] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 14:48:37
2025-08-02 15:10:38,839 [8] ERROR CommonLog 
 - 发起支付入参：price:138, orderNo:, remark:三年专业版, account:***********
2025-08-02 15:10:39,112 [8] ERROR CommonLog 
 - 发起支付结果 ,OrderId:2025080215104062519781,Url:http://ocr.oldfish.cn/ToPay.aspx?orderId=2025080215104062519781,重试次数:1
2025-08-02 15:40:46,210 [120] ERROR CommonLog 
 - 发起支付入参：price:158, orderNo:, remark:一年旗舰版, account:***********
2025-08-02 15:40:46,292 [120] ERROR CommonLog 
 - 发起支付结果 ,OrderId:2025080215404799628735,Url:http://ocr.oldfish.cn/ToPay.aspx?orderId=2025080215404799628735,重试次数:1
2025-08-02 15:56:49,456 [140] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 15:56:49
2025-08-02 17:17:41,941 [9] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 17:17:41
2025-08-02 17:28:54,728 [25] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 17:28:54
2025-08-02 17:58:47,822 [43] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 17:58:47
2025-08-02 21:41:34,145 [35] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 21:41:34
2025-08-02 21:41:41,476 [22] ERROR CommonLog 
 - 登录异常
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 System.Web.HttpResponse.Redirect(String url, Boolean endResponse, Boolean permanent)
   在 Account.Web.Login.ProcessLogin(String username, String password, Boolean isFormSubmit, String lang) 位置 D:\Code\Code\CodeManager\CodeManager\Login.aspx.cs:行号 233
2025-08-02 21:42:06,138 [22] ERROR CommonLog 
 - 登录异常
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 System.Web.HttpResponse.Redirect(String url, Boolean endResponse, Boolean permanent)
   在 Account.Web.Login.ProcessLogin(String username, String password, Boolean isFormSubmit, String lang) 位置 D:\Code\Code\CodeManager\CodeManager\Login.aspx.cs:行号 233
2025-08-02 21:42:30,993 [22] ERROR CommonLog 
 - 登录异常
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 System.Web.HttpResponse.Redirect(String url, Boolean endResponse, Boolean permanent)
   在 Account.Web.Login.ProcessLogin(String username, String password, Boolean isFormSubmit, String lang) 位置 D:\Code\Code\CodeManager\CodeManager\Login.aspx.cs:行号 233
2025-08-02 21:42:53,403 [60] ERROR CommonLog 
 - 发起支付入参：price:158, orderNo:, remark:一年旗舰版, account:***********
2025-08-02 21:42:53,477 [60] ERROR CommonLog 
 - 发起支付结果 ,OrderId:2025080221425544015360,Url:http://ocr.oldfish.cn/ToPay.aspx?orderId=2025080221425544015360,重试次数:1
2025-08-02 21:44:51,747 [34] ERROR CommonLog 
 - 登录异常
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 System.Web.HttpResponse.Redirect(String url, Boolean endResponse, Boolean permanent)
   在 Account.Web.Login.ProcessLogin(String username, String password, Boolean isFormSubmit, String lang) 位置 D:\Code\Code\CodeManager\CodeManager\Login.aspx.cs:行号 233
2025-08-02 21:58:01,239 [13] ERROR CommonLog 
 - 登录异常
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 System.Web.HttpResponse.Redirect(String url, Boolean endResponse, Boolean permanent)
   在 Account.Web.Login.ProcessLogin(String username, String password, Boolean isFormSubmit, String lang) 位置 D:\Code\Code\CodeManager\CodeManager\Login.aspx.cs:行号 233
2025-08-02 21:58:20,406 [55] ERROR CommonLog 
 - 登录异常
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 System.Web.HttpResponse.Redirect(String url, Boolean endResponse, Boolean permanent)
   在 Account.Web.Login.ProcessLogin(String username, String password, Boolean isFormSubmit, String lang) 位置 D:\Code\Code\CodeManager\CodeManager\Login.aspx.cs:行号 233
2025-08-02 21:59:08,561 [9] ERROR CommonLog 
 - 登录异常
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 System.Web.HttpResponse.Redirect(String url, Boolean endResponse, Boolean permanent)
   在 Account.Web.Login.ProcessLogin(String username, String password, Boolean isFormSubmit, String lang) 位置 D:\Code\Code\CodeManager\CodeManager\Login.aspx.cs:行号 233
2025-08-02 22:14:08,713 [53] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 22:14:08
2025-08-02 22:20:35,430 [25] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 22:20:35
2025-08-02 22:21:02,343 [18] ERROR CommonLog 
 - 写入JSON响应失败
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 Account.Web.Common.AuthHelper.WriteJsonResponse(HttpResponse response, Boolean success, String message, Object data) 位置 D:\Code\Code\CodeManager\CodeManager\Common\AuthHelper.cs:行号 212
2025-08-02 22:21:02,363 [18] ERROR CommonLog 
 - 写入JSON响应失败
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 Account.Web.Common.AuthHelper.WriteJsonResponse(HttpResponse response, Boolean success, String message, Object data) 位置 D:\Code\Code\CodeManager\CodeManager\Common\AuthHelper.cs:行号 212
2025-08-02 22:26:24,708 [51] ERROR CommonLog 
 - 写入JSON响应失败
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 Account.Web.Common.AuthHelper.WriteJsonResponse(HttpResponse response, Boolean success, String message, Object data) 位置 D:\Code\Code\CodeManager\CodeManager\Common\AuthHelper.cs:行号 212
2025-08-02 22:26:24,748 [51] ERROR CommonLog 
 - 写入JSON响应失败
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 Account.Web.Common.AuthHelper.WriteJsonResponse(HttpResponse response, Boolean success, String message, Object data) 位置 D:\Code\Code\CodeManager\CodeManager\Common\AuthHelper.cs:行号 212
2025-08-02 22:26:54,064 [17] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 22:26:54
2025-08-02 22:27:12,009 [28] ERROR CommonLog 
 - 写入JSON响应失败
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 Account.Web.Common.AuthHelper.WriteJsonResponse(HttpResponse response, Boolean success, String message, Object data) 位置 D:\Code\Code\CodeManager\CodeManager\Common\AuthHelper.cs:行号 212
2025-08-02 22:27:12,029 [28] ERROR CommonLog 
 - 写入JSON响应失败
System.Threading.ThreadAbortException: 正在中止线程。
   在 System.Threading.Thread.AbortInternal()
   在 System.Threading.Thread.Abort(Object stateInfo)
   在 System.Web.HttpResponse.AbortCurrentThread()
   在 Account.Web.Common.AuthHelper.WriteJsonResponse(HttpResponse response, Boolean success, String message, Object data) 位置 D:\Code\Code\CodeManager\CodeManager\Common\AuthHelper.cs:行号 212
2025-08-02 22:29:36,412 [9] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-02 22:29:36
