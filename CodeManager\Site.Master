<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.master.cs" Inherits="Account.Web.Site" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <link rel="icon" href="/favicon.ico" type="image/x-icon" />
    <meta name="robots" content="all" />
    <link rel="preconnect" href="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com" />
    <link rel="dns-prefetch" href="https://hm.baidu.com" />
    <link rel="dns-prefetch" href="https://cdn.oldfish.cn" />
    <script>
        var isWebP = (() => { try { return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0 } catch (e) { return false } })();
        document.addEventListener('DOMContentLoaded', () => {
            const c = document.getElementById('user-login-container');
            if (c) {
                fetch(c.getAttribute('data-url')).then(r => r.text()).then(h => {
                    c.innerHTML = h;
                    c.querySelectorAll('a').forEach(l => {
                        l.style.display = 'inline-block';
                        l.style.marginLeft = '10px';
                    });
                });
            }
        });
    </script>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
    <link rel="stylesheet" href="site/css/site.css" type="text/css" />
</head>
<body>
    <div class="home-container root">
        <header class="v4_header_pc">
            <div class="header_pc_left">
                <a href="Default.aspx" title="首页 | AI智能文字识别" class="pc-logo-wrap ml-4 mr-5">
                    <picture>
                        <source srcset="site/image/logo-s.png.webp" type="image/webp" />
                        <img src="site/image/logo-s.png" class="pc-logo" style="height: 100%" alt="" aria-hidden="true" />
                    </picture>
                    <span style="font-size: 18px; font-weight: bold; color: #333;">OCR文字识别助手</span>
                </a>
                <ul class="top-nav">
                    <li><a class="color-default" href="Default.aspx">首页</a></li>
                    <li><a class="color-default" data-sub="product-sub-nav" href="Detail.aspx">产品功能</a><div class="triangle"></div>
                    </li>
                    <li><a class="color-default" data-sub="experience-sub-nav" href="javascript:;">在线体验</a><div class="triangle"></div>
                    </li>
                    <li><a class="color-default" data-sub="vip-sub-nav" href="javascript:;">VIP会员</a><div class="triangle"></div>
                    </li>
                    <li><a class="color-default" data-sub="support-sub-nav" href="javascript:;">帮助支持</a><div class="triangle"></div>
                    </li>
                </ul>
                <div class="product-con">
                    <section id="product-sub-nav" class="pls-nav-dropdown" style="visibility: hidden; height: 0px; display: none;">
                        <div class="d-flex nav-drown-con" style="height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);">
                            <div class="prdocu-sub-left">
                                <div class="mr-auto">
                                    <picture>
                                        <source class="p-icon" srcset="site/image/icon/p_1.png.webp" type="image/webp" alt="" aria-hidden="true" />
                                        <img class="p-icon" src="site/image/icon/p_1.png" alt="_" aria-hidden="true" loading="lazy" />
                                    </picture>
                                    <div class="mt-4">
                                        <h3 class="h6">OCR文字识别助手</h3>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">提升效率·降低成本·创造价值</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">&nbsp;</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">智能识别·高速处理·精准输出</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">从文字到表格，从公式到翻译</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">让每一次文字处理都如此简单</p>
                                        <a class="link block disa fn14 contact-business shake" href="Detail.aspx">立即体验 <span class="iconfont4 icon-xiangzuo gt"></span></a>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white prdocu-sub-right">
                                <div>
                                    <h2 class="text-base-color h6">产品功能</h2>
                                    <div class="mt-4 d-flex flex-wrap">
                                        <a href="Detail.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">功能介绍</h3>
                                                <span class="color-gray fn14">详细了解OCR助手的核心功能和技术优势，98%+识别率</span>
                                            </div>
                                        </a>
                                        <a href="Download.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">软件下载</h3>
                                                <span class="color-gray fn14">下载最新版本客户端，支持Windows系统，含安装指南和更新日志</span>
                                            </div>
                                        </a>
                                        <a href="Desc.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">版本对比</h3>
                                                <span class="color-gray fn14">详细对比免费版、个人版、专业版、旗舰版功能差异</span>
                                            </div>
                                        </a>
                                        <a href="Status.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">服务状态</h3>
                                                <span class="color-gray fn14">实时监控全球识别节点运行状态，查看系统性能数据</span>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section id="experience-sub-nav" class="pls-nav-dropdown solution-part" style="visibility: hidden; height: 0px; display: none;">
                        <div class="d-flex nav-drown-con" style="height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);">
                            <div class="prdocu-sub-left">
                                <div class="mr-auto">
                                    <picture>
                                        <source srcset="site/image/icon/p_4.png.webp" type="image/webp" />
                                        <img class="p-icon" src="site/image/icon/p_4.png" alt="" aria-hidden="true" loading="lazy" />
                                    </picture>
                                    <div class="mt-4">
                                        <h3 class="h6">OCR文字识别助手</h3>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">文字、表格、公式、文档、翻译</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">&nbsp;</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">三步完成所有文字处理需求</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">截图 → 识别 → 应用</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">让工作效率提升300%</p>
                                        <a class="link block disa fn14 contact-business shake" href="Detail.aspx">立即体验 <span class="iconfont4 icon-xiangzuo gt"></span></a>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white prdocu-sub-right">
                                <div>
                                    <div class="mt-4 d-flex flex-wrap">
                                        <div style="width: 100%; margin-bottom: 15px;">
                                            <h4 class="h6" style="color: #007cfa; margin-bottom: 10px;">OCR功能体验</h4>
                                        </div>
                                        <a href="Ocr.aspx?type=index" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">全部功能</h3>
                                                <span class="color-gray fn14">一站式体验所有OCR智能功能，快速找到适合您需求的最佳解决方案</span>
                                            </div>
                                        </a>
                                        <a href="Ocr.aspx?type=text_recognize" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">通用文字识别</h3>
                                                <span class="color-gray fn14">多语种高精度文字智能提取，支持印刷体与多场景复杂图片识别</span>
                                            </div>
                                        </a>
                                        <a href="Ocr.aspx?type=table" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">通用表格识别</h3>
                                                <span class="color-gray fn14">表格图片智能转Excel文件，自动处理复杂表结构和合并单元格</span>
                                            </div>
                                        </a>
                                        <a href="Ocr.aspx?type=handwritten_ocr" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">手写识别</h3>
                                                <span class="color-gray fn14">智能识别中英文手写内容，支持课堂笔记、病历记录等场景</span>
                                            </div>
                                        </a>
                                        <a href="Ocr.aspx?type=pdf2word" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">PDF转Word</h3>
                                                <span class="color-gray fn14">PDF文档快速转为Word格式，完美保留原始排版和图文布局</span>
                                            </div>
                                        </a>
                                        <a href="Ocr.aspx?type=pdf2markdown" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">PDF转Markdown</h3>
                                                <span class="color-gray fn14">PDF文档智能转为MD格式，代码块和文本结构自动优化处理</span>
                                            </div>
                                        </a>
                                        <div style="width: 100%; margin: 15px 0;">
                                            <h4 class="h6" style="color: #007cfa; margin-bottom: 10px;">文档处理工具</h4>
                                        </div>
                                        <a href="Ocr.aspx?type=word2pdf" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">Word转PDF</h3>
                                                <span class="color-gray fn14">Word文档一键转PDF，完美保留原格式，适合存档和正式文件分享</span>
                                            </div>
                                        </a>
                                        <a href="Ocr.aspx?type=word2jpg" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">Word转图片</h3>
                                                <span class="color-gray fn14">Word文档智能转JPG图片，支持多页处理，便于社交媒体分享</span>
                                            </div>
                                        </a>
                                        <a href="Ocr.aspx?type=pdf2jpg" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">PDF转图片</h3>
                                                <span class="color-gray fn14">PDF文档高清转换为JPG图片，支持批量处理和自定义分辨率</span>
                                            </div>
                                        </a>
                                        <a href="Ocr.aspx?type=image2pdf" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">图片转PDF</h3>
                                                <span class="color-gray fn14">多张图片合并为PDF文档，支持排序和页面设置</span>
                                            </div>
                                        </a>
                                        <div style="width: 100%; margin: 15px 0;">
                                            <h4 class="h6" style="color: #007cfa; margin-bottom: 10px;">开发者工具</h4>
                                        </div>
                                        <a href="Tool.aspx?type=Json" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">JSON格式化</h3>
                                                <span class="color-gray fn14">智能美化JSON代码结构，支持压缩和展开，便于开发调试</span>
                                            </div>
                                        </a>
                                        <a href="Tool.aspx?type=Diff" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">文本比对合并</h3>
                                                <span class="color-gray fn14">高亮显示文本差异，支持逐行对比和智能合并</span>
                                            </div>
                                        </a>
                                        <a href="Tool.aspx?type=Encode" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">文本编码转换</h3>
                                                <span class="color-gray fn14">支持Base64/URL/Unicode等多种编码格式互相转换</span>
                                            </div>
                                        </a>
                                        <a href="Tool.aspx?type=Regex" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">正则表达式</h3>
                                                <span class="color-gray fn14">实时验证正则表达式匹配效果，内置常用模式库</span>
                                            </div>
                                        </a>
                                        <a href="Tool.aspx?type=Count" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">字数统计</h3>
                                                <span class="color-gray fn14">智能统计字符、词汇和段落数量，自动优化文本排版</span>
                                            </div>
                                        </a>
                                        <a href="Tool.aspx?type=Timespan" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">时间戳转换</h3>
                                                <span class="color-gray fn14">时间与Unix时间戳互相转换，支持多种格式和时区设置</span>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section id="vip-sub-nav" class="pls-nav-dropdown solution-part" style="visibility: hidden; height: 0px; display: none;">
                        <div class="d-flex nav-drown-con" style="height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);">
                            <div class="prdocu-sub-left">
                                <div class="mr-auto">
                                    <picture>
                                        <source class="p-icon" srcset="site/image/icon/p_1.png.webp" type="image/webp" alt="" aria-hidden="true" />
                                        <img class="p-icon" src="site/image/icon/p_1.png" alt="" aria-hidden="true" loading="lazy" />
                                    </picture>
                                    <div class="mt-4">
                                        <h3 class="h6">VIP会员服务</h3>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">解锁全部高级功能·享受专属服务</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">&nbsp;</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">离线识别·批量处理·无限制使用</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">专业版 → 旗舰版 → 企业版</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">总有一款适合您的需求</p>
                                        <a class="link block disa fn14 contact-business shake" href="Version.aspx">查看详情 <span class="iconfont4 icon-xiangzuo gt"></span></a>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white prdocu-sub-right">
                                <div>
                                    <h2 class="text-base-color h6" style="margin: inherit;">VIP会员</h2>
                                    <div class="mt-4 d-flex flex-wrap">
                                        <a href="Version.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">会员权益</h3>
                                                <span class="color-gray fn14">详细了解各版本功能差异，选择最适合您的会员等级</span>
                                            </div>
                                        </a>
                                        <a href="MemberUpgrade.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">会员升级</h3>
                                                <span class="color-gray fn14">快速升级VIP会员，解锁更多高级功能和专属服务</span>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section id="support-sub-nav" class="pls-nav-dropdown solution-part" style="visibility: hidden; height: 0px; display: none;">
                        <div class="d-flex nav-drown-con" style="height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);">
                            <div class="prdocu-sub-left">
                                <div class="mr-auto">
                                    <picture>
                                        <source class="p-icon" srcset="site/image/icon/p_4.png.webp" type="image/webp" alt="" aria-hidden="true" />
                                        <img class="p-icon" src="site/image/icon/p_4.png" alt="" aria-hidden="true" loading="lazy" />
                                    </picture>
                                    <div class="mt-4">
                                        <h3 class="h6">帮助支持中心</h3>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">专业客服·详细文档·快速响应</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">&nbsp;</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">遇到问题不用慌</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">问题 → 查找 → 解决</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">让您的使用体验更顺畅</p>
                                        <a class="link block disa fn14 contact-business shake" href="FAQ.aspx">获取帮助 <span class="iconfont4 icon-xiangzuo gt"></span></a>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white prdocu-sub-right">
                                <div>
                                    <h2 class="text-base-color h6" style="margin: inherit;">帮助支持</h2>
                                    <div class="mt-4 d-flex flex-wrap">
                                        <a href="FAQ.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">常见问题</h3>
                                                <span class="color-gray fn14">快速解答用户常见疑问，提供详细的使用指南和技术支持</span>
                                            </div>
                                        </a>
                                        <a href="AboutUs.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">关于我们</h3>
                                                <span class="color-gray fn14">了解OCR文字识别助手的发展历程、核心功能和服务理念</span>
                                            </div>
                                        </a>
                                        <a href="UserAgreement.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">用户协议</h3>
                                                <span class="color-gray fn14">详细的服务条款和用户权利义务说明</span>
                                            </div>
                                        </a>
                                        <a href="PrivacyPolicy.aspx" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h3 class="h6">隐私协议</h3>
                                                <span class="color-gray fn14">个人信息保护政策和数据安全保障措施</span>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
            <div class="header_pc_right" style="display: flex; align-items: center; justify-content: flex-end;">
                <div id="translate"></div>
                <div id="user-login-container" data-url="User.ashx?op=userinfo" style="white-space: nowrap; padding-right: 20px;"></div>
            </div>
        </header>
        <header class="v4_header_mob">
            <div class="container">
                <div class="row no-gutters justify-content-between align-items-center">
                    <a href="Default.aspx" title="首页 | AI智能文字识别" class="mob-logo-wrap">
                        <picture>
                            <source srcset="site/image/logo-s.png.webp" type="image/webp" />
                            <img src="site/image/logo-s.png" class="pc-logo" style="height: 32px; width: 32px" alt="" aria-hidden="true" />
                        </picture>
                        <span style="font-size: 18px; font-weight: bold; color: #333;">OCR文字识别助手</span>
                    </a>
                    <div class="right-menu" role="button" tabindex="0" aria-label="打开导航菜单" data-toggle="collapse" data-target="#mob-header-collapse" aria-expanded="false" aria-controls="mob-header-collapse">
                        <nav-products id="nav_products">
                            <nav-button class="submail-mdi-nav-btn" style="width: 60px">
                                <label id="mdi-submail-nav-btn">
                                    <span class="mdi-nav-products-1">
                                        <span class="nav-products-icon"></span>
                                    </span>
                                    <span class="mdi-nav-products-2">
                                        <span class="nav-products-icon"></span>
                                    </span>
                                    <span class="mdi-nav-products-3">
                                        <span class="nav-products-icon"></span>
                                    </span>
                                </label>
                            </nav-button>
                        </nav-products>
                    </div>
                </div>
            </div>
            <div class="mob-nav-content">
                <div class="sidebar-fix">
                    <div class="container">
                        <div class="row">
                            <div class="col-4 bg-white sidebar-fix-left" style="border-right: 1px solid #e1e6ed">
                                <div class="mob-nav-item active" data="0">
                                    <div class="nav-header"><a href="Default.aspx" class="nav-header-letter px-3"><span>首页</span></a></div>
                                </div>
                                <div class="mob-nav-item" data="1">
                                    <div class="nav-header"><a href="#" class="nav-header-letter px-3"><span>产品</span></a></div>
                                </div>
                                <div class="mob-nav-item" data="2">
                                    <div class="nav-header"><a href="#" class="nav-header-letter px-3"><span>会员</span></a></div>
                                </div>
                                <div class="mob-nav-item" data="3">
                                    <div class="nav-header"><a href="#" class="nav-header-letter px-3"><span>更多</span></a></div>
                                </div>
                            </div>
                            <div class="col-8 bg-white sidebar-fix-rigth">
                                <!-- 首页内容 -->
                                <div class="sub-nav">
                                    <div class="sub-nav-item py-2 pb-3 px-4">
                                        <h2 class="h5" style="margin: inherit;">高效率生产力工具</h2>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">智能识别·高速处理·精准输出</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">&nbsp;</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">3秒识别整页文档</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">98%+识别准确率</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">多语种实时处理无延迟</p>
                                        <a class="link block disa fn14 contact-business shake" href="Download.aspx">立即下载体验<span class="iconfont4 icon-xiangzuo gt"></span></a>
                                    </div>
                                </div>
                                <!-- 产品功能内容 -->
                                <div class="sub-nav">
                                    <div class="sub-nav-item py-3 px-4">
                                        <h2 class="h5" style="margin: inherit;">产品功能</h2>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">AI智能识别，一站式解决方案</p>
                                    </div>
                                    <div class="sub-nav-item py-3 px-4"><a href="Detail.aspx">功能介绍</a></div>
                                    <div class="sub-nav-item py-3 px-4"><a href="Download.aspx">软件下载</a></div>
                                    <div class="sub-nav-item py-3 px-4"><a href="Desc.aspx">版本对比</a></div>
                                    <div class="sub-nav-item py-3 px-4"><a href="Status.aspx">服务状态</a></div>
                                    <div class="sub-nav-item py-3 px-4"><a href="Ocr.aspx">在线体验</a></div>
                                </div>
                                <!-- VIP会员内容 -->
                                <div class="sub-nav">
                                    <div class="sub-nav-item py-3 px-4">
                                        <h2 class="h5" style="margin: inherit;">VIP会员服务</h2>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">解锁全部功能，享受专属服务</p>
                                    </div>
                                    <div class="sub-nav-item py-3 px-4"><a href="Version.aspx">会员权益</a></div>
                                    <div class="sub-nav-item py-3 px-4"><a href="MemberUpgrade.aspx">立即开通</a></div>
                                </div>
                                <!-- 更多内容 -->
                                <div class="sub-nav">
                                    <div class="sub-nav-item py-3 px-4">
                                        <h2 class="h5" style="margin: inherit;">帮助与支持</h2>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">专业客服，贴心服务</p>
                                    </div>
                                    <div class="sub-nav-item py-3 px-4"><a href="FAQ.aspx">使用帮助</a></div>
                                    <div class="sub-nav-item py-3 px-4"><a href="AboutUs.aspx">关于我们</a></div>
                                    <div class="sub-nav-item py-3 px-4"><a href="mailto:<EMAIL>">联系客服</a></div>
                                    <div class="sub-nav-item py-3 px-4"><a href="UserAgreement.aspx">服务条款</a></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        <form id="form1" runat="server">
            <div>
                <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                </asp:ContentPlaceHolder>
            </div>
        </form>

        <div class="fixed-icon">
            <div style="width: 120px; position: relative; left: 1rem; top: -12px;">
            </div>
            <a id="webchat" class="wx-con disa" href="http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes" target="_blank">
                <img class="wechat-ico" src="site/image/icon/qq.svg" alt="QQ客服" />
                <div class="wx-text">
                    QQ客服(365833440)
                </div>
                <label class="badge badge-danger" style="background: rgb(255, 34, 122); display: inline-block;">1</label>
            </a>

            <a class="wx-con" href="https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1" target="_blank">
                <img class="wechat-ico" src="site/image/icon/qq.svg" alt="QQ群" />
                <div class="wx-text">
                    QQ群(100029010)
                </div>
                <label class="badge badge-danger" style="background: rgb(255, 34, 122); display: inline-block;">2</label>
            </a>
            <a href="mailto:<EMAIL>" class="wx-con">
                <img class="wechat-ico" src="site/image/icon/im.svg" alt="Email" />
                <div class="wx-text" style="width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;">
                    <div style="font-size: 15px">
                        邮箱:<EMAIL>
                    </div>
                    <hr style="background: #c1c1c1; margin: 10px 10px 6px 0;" />
                    <span style="font-size: 13px; color: #c1c1c1">感谢您的意见和建议！</span>
                </div>
                <label class="badge badge-danger" style="background: rgb(255, 34, 122); display: inline-block;">3</label>
            </a>
        </div>

        <style>
            .content {
                width: 100%;
                padding: 0 !important;
                margin: 0 auto
            }

            h3.h6 {
                font-size: 1rem !important;
                font-weight: bold !important;
                line-height: 1.2 !important;
                margin-bottom: .5rem;
                font-family: inherit
            }
        </style>
        <footer class="page-footer">
            <div class="container">
                <div class="row foot-cont pt-4 no-gutters" style="border-top: 1px solid #e1e6ed; max-width: 100%">
                    <div class="col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray" style="text-align: center;">
                        OCR文字识别助手&nbsp;©️ <%=DateTime.Now.Year %> ALL RIGHTS RESERVED. 保留所有权利&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
                        <a href="PrivacyPolicy.aspx" style="text-decoration: underline;">隐私协议</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
                        <a href="UserAgreement.aspx" style="text-decoration: underline;">用户协议</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
                        <a href="Status.aspx" style="text-decoration: underline;">服务状态</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
                        <a class="mb-2 color-gray" href="https://beian.miit.gov.cn/" target="_blank" style="text-decoration: underline;">鄂 ICP 备 2021012692 号</a>
                    </div>
                </div>
            </div>
        </footer>
        <style>
            #translate > .translateSelectLanguage {
                right: 2rem;
                font-size: 1rem;
                width: 150px;
                padding: .3rem;
                margin-right: 20px;
                border: 1px solid #C9C9C9;
                background: #fff;
                color: #555
            }
        </style>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const setScroll = active => document.querySelectorAll('.v4_header_pc,.v4_header_mob').forEach(e => e.classList[active ? 'add' : 'remove']('scrollActive'));
            window.pageYOffset > 60 && setScroll(1);
            window.addEventListener('scroll', () => setScroll(window.pageYOffset > 60));

            document.querySelectorAll('.v4_header_pc').forEach(h => {
                h.addEventListener('mouseenter', () => h.classList.add('active'));
                h.addEventListener('mouseleave', () => h.classList.remove('active'))
            });

            let menuTimer;
            const hideMenu = (menuId) => {
                const m = document.getElementById(menuId);
                if (m) {
                    m.style.cssText = 'visibility:hidden;height:0;display:none';
                    const n = m.querySelector('.nav-drown-con');
                    n && (n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)')
                }
            };

            document.querySelectorAll('ul.top-nav>li').forEach(i => {
                i.addEventListener('mouseenter', function () {
                    clearTimeout(menuTimer);
                    const l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l?.getAttribute('data-sub');
                    l?.classList.add('active'); t?.classList.add('active');
                    if (s) {
                        const m = document.getElementById(s);
                        if (m) {
                            m.style.cssText = 'display:block;visibility:visible';
                            Array.from(m.parentNode.children).filter(c => c !== m).forEach(c => c.style.display = 'none');
                            const n = m.querySelector('.nav-drown-con');
                            n && (n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)');

                            m.addEventListener('mouseenter', () => clearTimeout(menuTimer));
                            m.addEventListener('mouseleave', () => menuTimer = setTimeout(() => hideMenu(s), 100))
                        }
                    }
                });
                i.addEventListener('mouseleave', function () {
                    const l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l?.getAttribute('data-sub');
                    l?.classList.remove('active'); t?.classList.remove('active');
                    if (s) {
                        menuTimer = setTimeout(() => hideMenu(s), 100)
                    }
                })
            });

            const r = document.querySelector('.v4_header_mob .right-menu');
            r && r.addEventListener('click', function () {
                [this, '.v4_header_mob', '#nav_products', '.mob-nav-content .sidebar-fix'].forEach((s, i) => {
                    const e = typeof s === 'string' ? document.querySelector(s) : s;
                    e && e.classList.toggle(['active', 'active', 'active1', 'fixedActiveBg'][i])
                });
                const f = document.querySelector('.mob-nav-content .sidebar-fix');
                f && f.classList.toggle('show');
                document.body.style.overflow = f?.classList.contains('show') ? 'hidden' : 'scroll'
            });

            document.addEventListener('click', e => {
                const m = e.target.closest('.sidebar-fix-left .mob-nav-item');
                if (m) {
                    Array.from(m.parentNode.children).forEach(c => c.classList.remove('active'));
                    m.classList.add('active');
                    const d = m.getAttribute('data');
                    d && document.querySelectorAll('.sidebar-fix-rigth .sub-nav').forEach((n, i) => n.style.display = i == d ? 'block' : 'none')
                }
            })
        });
    </script>
</body>
</html>
