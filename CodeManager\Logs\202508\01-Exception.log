﻿2025-08-01 00:18:29,710 [72] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 00:18:29
2025-08-01 09:29:19,500 [96] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 09:29:19
2025-08-01 09:51:49,942 [27] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 09:51:49
2025-08-01 10:36:48,362 [36] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 10:36:48
2025-08-01 10:57:31,001 [43] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 10:57:30
2025-08-01 12:00:20,353 [38] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 12:00:20
2025-08-01 14:57:30,051 [46] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 14:57:30
2025-08-01 17:07:50,574 [281] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 17:07:50
2025-08-01 18:32:40,818 [233] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 18:32:40
2025-08-01 18:32:52,940 [241] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpParseException (0x80004005): 未能加载类型“Account.Web.User”。 ---> System.Web.HttpParseException (0x80004005): 未能加载类型“Account.Web.User”。 ---> System.Web.HttpException (0x80004005): 未能加载类型“Account.Web.User”。
   在 System.Web.UI.TemplateParser.GetType(String typeName, Boolean ignoreCase, Boolean throwOnError)
   在 System.Web.UI.TemplateParser.ProcessInheritsAttribute(String baseTypeName, String codeFileBaseTypeName, String src, Assembly assembly)
   在 System.Web.UI.TemplateParser.PostProcessMainDirectiveAttributes(IDictionary parseData)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 18:33:29,902 [215] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): 未能加载类型“Account.Web.Login”。 ---> System.Web.HttpParseException (0x80004005): 未能加载类型“Account.Web.Login”。 ---> System.Web.HttpException (0x80004005): 未能加载类型“Account.Web.Login”。
   在 System.Web.UI.TemplateParser.GetType(String typeName, Boolean ignoreCase, Boolean throwOnError)
   在 System.Web.UI.TemplateParser.ProcessInheritsAttribute(String baseTypeName, String codeFileBaseTypeName, String src, Assembly assembly)
   在 System.Web.UI.TemplateParser.PostProcessMainDirectiveAttributes(IDictionary parseData)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 18:34:48,109 [255] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 18:34:48
2025-08-01 18:34:54,639 [223] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_e2ofmars.14.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_5rhheo0g.4.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 18:35:21,035 [176] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_e2ofmars.14.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_5rhheo0g.4.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 18:37:15,288 [237] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_ylo5mbns.14.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_asnjgjzk.7.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 18:37:39,839 [216] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_y2z01gtd.12.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_asnjgjzk.7.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 18:39:04,015 [243] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 18:39:04
2025-08-01 18:43:21,325 [290] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_egch4bvo.3.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 18:43:48,074 [273] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_egch4bvo.3.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 18:44:32,648 [301] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_egch4bvo.3.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:05:59,794 [274] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_f4ystci4.5.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:06:09,342 [309] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_f4ystci4.5.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:06:42,671 [211] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_f4ystci4.5.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:06:52,923 [274] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_ww4nkcpo.2.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:06:56,112 [276] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_ww4nkcpo.2.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:12:04,590 [271] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_h0igy4l0.2.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:12:24,804 [204] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_h0igy4l0.2.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:12:42,711 [271] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_s4ojss2s.3.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:12:59,767 [284] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_pbxev4av.1.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:13:02,247 [195] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_pbxev4av.1.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:13:54,543 [292] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_pbxev4av.1.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:16:15,217 [296] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_wromubqk.0.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:17:05,548 [201] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Web.HttpException (0x80004005): 一页只能有一个服务器端 Form 标记。
   在 System.Web.UI.Page.OnFormRender()
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderChildren(HtmlTextWriter writer)
   在 System.Web.UI.HtmlControls.HtmlContainerControl.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.HtmlControls.HtmlForm.RenderControl(HtmlTextWriter writer)
   在 ASP.site_master.__Render__control1(HtmlTextWriter __w, Control parameterContainer) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_hmhsu353.1.cs:行号 0
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Control.RenderChildrenInternal(HtmlTextWriter writer, ICollection children)
   在 System.Web.UI.Page.Render(HtmlTextWriter writer)
   在 System.Web.UI.Control.RenderControlInternal(HtmlTextWriter writer, ControlAdapter adapter)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.login_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_pxmooihd.3.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:19:19,252 [290] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:20:17,701 [204] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:20:37,992 [301] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:21:05,771 [279] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 19:21:05
2025-08-01 19:21:16,465 [205] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:21:36,625 [279] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:21:49,589 [276] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:23:28,667 [206] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:23:55,574 [10] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 19:24:20,839 [10] ERROR Application_Error 
 -  URL:http://localhost:19225/Login.aspx
System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpParseException (0x80004005): “login-page-form”不是有效标识符。 ---> System.Web.HttpException (0x80004005): “login-page-form”不是有效标识符。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 21:17:24,923 [46] ERROR Application_Error 
 -  URL:http://localhost:19225/UserForgetPwd.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\UserForgetPwd.aspx(188): error CS1061: “ASP.userforgetpwd_aspx”不包含“btnForgetPwd_Click”的定义，并且找不到可接受类型为“ASP.userforgetpwd_aspx”的第一个参数的扩展方法“btnForgetPwd_Click”(是否缺少 using 指令或程序集引用?)
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 21:17:29,951 [58] ERROR Application_Error 
 -  URL:http://localhost:19225/UserForgetPwd.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\UserForgetPwd.aspx(188): error CS1061: “ASP.userforgetpwd_aspx”不包含“btnForgetPwd_Click”的定义，并且找不到可接受类型为“ASP.userforgetpwd_aspx”的第一个参数的扩展方法“btnForgetPwd_Click”(是否缺少 using 指令或程序集引用?)
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 21:17:48,127 [51] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 21:17:48
2025-08-01 21:48:02,279 [67] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 21:48:02
2025-08-01 22:43:38,327 [24] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 22:43:38
2025-08-01 22:49:48,449 [10] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9,en;q=0.8
HTTP_COOKIE:lang=zh-Hans; token=89ba32d920624f43a2331d9cae944d9d; account=***********
HTTP_HOST:localhost:19225
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA:"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_UPGRADE_INSECURE_REQUESTS:1
HTTP_SEC_FETCH_SITE:none
HTTP_SEC_FETCH_MODE:navigate
HTTP_SEC_FETCH_USER:?1
HTTP_SEC_FETCH_DEST:document
|ALL_RAW->Connection: keep-alive
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
Cookie: lang=zh-Hans; token=89ba32d920624f43a2331d9cae944d9d; account=***********
Host: localhost:19225
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Sec-Fetch-Site: none
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/Index.aspx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\Index.aspx|QUERY_STRING->|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->51818|REQUEST_METHOD->GET|SCRIPT_NAME->/Index.aspx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/Index.aspx|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9,en;q=0.8|HTTP_COOKIE->lang=zh-Hans; token=89ba32d920624f43a2331d9cae944d9d; account=***********|HTTP_HOST->localhost:19225|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_UPGRADE_INSECURE_REQUESTS->1|HTTP_SEC_FETCH_SITE->none|HTTP_SEC_FETCH_MODE->navigate|HTTP_SEC_FETCH_USER->?1|HTTP_SEC_FETCH_DEST->document
GET:http://localhost:19225/Index.aspx
Header:Connection->keep-alive|Accept->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9,en;q=0.8|Cookie->lang=zh-Hans; token=89ba32d920624f43a2331d9cae944d9d; account=***********|Host->localhost:19225|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|sec-ch-ua-mobile->?0|sec-ch-ua-platform->"Windows"|Upgrade-Insecure-Requests->1|Sec-Fetch-Site->none|Sec-Fetch-Mode->navigate|Sec-Fetch-User->?1|Sec-Fetch-Dest->document
Cookies:lang->zh-Hans|token->89ba32d920624f43a2331d9cae944d9d|account->***********

2025-08-01 22:49:48,508 [10] ERROR CommonLog 
 - 路径不存在:index.aspx被拦截！原因：ResourceNotFound
IP: 
URL: http://localhost:19225/Index.aspx
Method: GET
Referer: 
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-08-01 22:57:23,118 [7] ERROR Application_End 
 - 【POD挂了】 时间:2025-08-01 22:57:23
2025-08-01 23:19:46,284 [56] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(687): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:19:48,980 [51] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(687): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:21:19,960 [70] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(971): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:22:19,933 [16] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(971): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:22:37,439 [14] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(971): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:22:39,001 [53] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(971): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:23:17,411 [73] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(971): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:23:38,784 [9] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(971): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:42:29,244 [14] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1249): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:42:30,962 [60] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1249): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:42:45,302 [20] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1249): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:42:46,128 [44] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1249): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:55:07,650 [55] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1408): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-08-01 23:55:37,003 [23] ERROR Application_Error 
 -  URL:http://localhost:19225/User.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\User.aspx(1408): error CS0128: 已在此范围定义了名为“lstAllType”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
